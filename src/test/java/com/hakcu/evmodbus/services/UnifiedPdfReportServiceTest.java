package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Floor;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.utils.SvgToPdfImageConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Test class for UnifiedPdfReportService.
 * Verifies that the unified PDF generation system works correctly for all report types
 * with the new multi-page layout (summary page + detailed data pages).
 */
@ExtendWith(MockitoExtension.class)
class UnifiedPdfReportServiceTest {

    @Mock
    private StatisticsService statisticsService;

    @Mock
    private ChartService chartService;

    @Mock
    private SvgToPdfImageConverter svgConverter;

    private UnifiedPdfReportService unifiedPdfReportService;

    private Spot testSpot;

    @BeforeEach
    void setUp() {
        unifiedPdfReportService = new UnifiedPdfReportService(statisticsService, chartService, svgConverter);

        // Create test spot
        Floor testFloor = new Floor();
        testFloor.setFloorNumber(1);

        testSpot = new Spot();
        testSpot.setId(1L);
        testSpot.setSpotNumber(5);
        testSpot.setFloor(testFloor);
    }

    @Test
    void testGenerateUnifiedReport_DailyReport() {
        // Arrange
        Long spotId = 1L;
        String reportType = "day";
        LocalDate selectedDay = LocalDate.of(2024, 1, 15);

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));
        when(statisticsService.getTotalConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(25.5f);
        when(statisticsService.getHourlyConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(createMockHourlyData());
        when(chartService.generateBarChartSvg(any(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn("<svg>mock chart</svg>");

        // Act
        ByteArrayOutputStream result = unifiedPdfReportService.generateUnifiedReport(
                spotId, reportType, selectedDay, null, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    @Test
    void testGenerateUnifiedReport_MonthlyReport() {
        // Arrange
        Long spotId = 1L;
        String reportType = "month";
        String selectedMonth = "2024-01";

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));
        when(statisticsService.getTotalConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(750.0f);
        when(statisticsService.getDailyConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(createMockDailyData());
        when(chartService.generateBarChartSvg(any(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn("<svg>mock chart</svg>");

        // Act
        ByteArrayOutputStream result = unifiedPdfReportService.generateUnifiedReport(
                spotId, reportType, null, selectedMonth, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    @Test
    void testGenerateUnifiedReport_YearlyReport() {
        // Arrange
        Long spotId = 1L;
        String reportType = "year";
        Integer selectedYear = 2024;

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));
        when(statisticsService.getTotalConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(9000.0f);
        when(statisticsService.getMonthlyConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(createMockMonthlyData());
        when(chartService.generateBarChartSvg(any(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn("<svg>mock chart</svg>");

        // Act
        ByteArrayOutputStream result = unifiedPdfReportService.generateUnifiedReport(
                spotId, reportType, null, null, selectedYear);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    @Test
    void testGenerateUnifiedReport_InvalidReportType() {
        // Arrange
        Long spotId = 1L;
        String reportType = "invalid";

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            unifiedPdfReportService.generateUnifiedReport(spotId, reportType, null, null, null);
        });
    }

    @Test
    void testGenerateUnifiedReport_SpotNotFound() {
        // Arrange
        Long spotId = 999L;
        String reportType = "day";

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            unifiedPdfReportService.generateUnifiedReport(spotId, reportType, null, null, null);
        });
    }

    @Test
    void testGenerateUnifiedReport_MultiPageLayout() {
        // Arrange
        Long spotId = 1L;
        String reportType = "month";
        String selectedMonth = "2024-01";

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));
        when(statisticsService.getTotalConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(750.0f);

        // Create large dataset to test pagination
        Map<String, Float> largeDailyData = createLargeDailyDataset();
        when(statisticsService.getDailyConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(largeDailyData);
        when(chartService.generateBarChartSvg(any(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn("<svg>mock chart</svg>");

        // Act
        ByteArrayOutputStream result = unifiedPdfReportService.generateUnifiedReport(
                spotId, reportType, null, selectedMonth, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        // The PDF should be larger due to multi-page layout
        assertTrue(result.size() > 1000); // Minimum expected size for multi-page PDF
    }

    @Test
    void testGenerateUnifiedReport_EmptyDataHandling() {
        // Arrange
        Long spotId = 1L;
        String reportType = "day";
        LocalDate selectedDay = LocalDate.of(2024, 1, 15);

        when(statisticsService.getSpotById(spotId)).thenReturn(Optional.of(testSpot));
        when(statisticsService.getTotalConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(0.0f);
        when(statisticsService.getHourlyConsumption(eq(spotId), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new LinkedHashMap<>()); // Empty data
        when(chartService.generateBarChartSvg(any(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn("<svg>mock chart</svg>");

        // Act
        ByteArrayOutputStream result = unifiedPdfReportService.generateUnifiedReport(
                spotId, reportType, selectedDay, null, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        // Should still generate a PDF with summary page and empty data message
    }

    private Map<String, Float> createMockHourlyData() {
        Map<String, Float> data = new LinkedHashMap<>();
        for (int hour = 0; hour < 24; hour++) {
            data.put(String.format("%02d:00", hour), (float) (Math.random() * 5));
        }
        return data;
    }

    private Map<String, Float> createMockDailyData() {
        Map<String, Float> data = new LinkedHashMap<>();
        for (int day = 1; day <= 31; day++) {
            data.put(String.format("%02d", day), (float) (Math.random() * 50));
        }
        return data;
    }

    private Map<String, Float> createMockMonthlyData() {
        Map<String, Float> data = new LinkedHashMap<>();
        String[] months = {"Ene", "Feb", "Mar", "Abr", "May", "Jun",
                          "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"};
        for (String month : months) {
            data.put(month, (float) (Math.random() * 1000));
        }
        return data;
    }

    private Map<String, Float> createLargeDailyDataset() {
        Map<String, Float> data = new LinkedHashMap<>();
        // Create 50 days of data to test pagination (exceeds MAX_ROWS_PER_PAGE)
        for (int day = 1; day <= 50; day++) {
            data.put(String.format("%02d", day), (float) (Math.random() * 50));
        }
        return data;
    }
}

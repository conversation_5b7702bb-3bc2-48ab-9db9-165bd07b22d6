package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Billing;
import com.hakcu.evmodbus.entities.BillingItem;
import com.hakcu.evmodbus.enums.BillingStatus;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Service for exporting billing data in various formats.
 */
@Service
public class BillingExportService {

    private final BillingPdfService billingPdfService;

    public BillingExportService(BillingPdfService billingPdfService) {
        this.billingPdfService = billingPdfService;
    }

    /**
     * Export billings to Excel format.
     *
     * @param billings List of billings to export
     * @param includeCustomerDetails Include customer details
     * @param includeItemDetails Include item details
     * @param includeComments Include comments
     * @return Excel file as byte array
     */
    public byte[] exportToExcel(List<Billing> billings, boolean includeCustomerDetails, 
                               boolean includeItemDetails, boolean includeComments) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Facturas");
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            int colIndex = 0;
            
            headerRow.createCell(colIndex++).setCellValue("Nº Factura");
            headerRow.createCell(colIndex++).setCellValue("Cliente");
            headerRow.createCell(colIndex++).setCellValue("Período");
            headerRow.createCell(colIndex++).setCellValue("Fecha Emisión");
            headerRow.createCell(colIndex++).setCellValue("Fecha Vencimiento");
            headerRow.createCell(colIndex++).setCellValue("Estado");
            headerRow.createCell(colIndex++).setCellValue("Tarifa");
            headerRow.createCell(colIndex++).setCellValue("Consumo Total (kWh)");
            headerRow.createCell(colIndex++).setCellValue("Cuota Fija (€)");
            headerRow.createCell(colIndex++).setCellValue("Consumo (€)");
            headerRow.createCell(colIndex++).setCellValue("Descuento (€)");
            headerRow.createCell(colIndex++).setCellValue("IVA (€)");
            headerRow.createCell(colIndex++).setCellValue("Total (€)");
            
            if (includeCustomerDetails) {
                headerRow.createCell(colIndex++).setCellValue("DNI Cliente");
                headerRow.createCell(colIndex++).setCellValue("Email Cliente");
                headerRow.createCell(colIndex++).setCellValue("Teléfono Cliente");
                headerRow.createCell(colIndex++).setCellValue("Dirección Cliente");
            }
            
            if (includeComments) {
                headerRow.createCell(colIndex++).setCellValue("Comentarios");
            }
            
            // Add data rows
            int rowIndex = 1;
            for (Billing billing : billings) {
                Row row = sheet.createRow(rowIndex++);
                colIndex = 0;
                
                row.createCell(colIndex++).setCellValue(billing.getBillNumber());
                row.createCell(colIndex++).setCellValue(billing.getCustomer().getFirstName() + " " + billing.getCustomer().getLastName());
                row.createCell(colIndex++).setCellValue(billing.getBillingPeriod().toString());
                row.createCell(colIndex++).setCellValue(billing.getIssueDate().toString());
                row.createCell(colIndex++).setCellValue(billing.getDueDate() != null ? billing.getDueDate().toString() : "");
                row.createCell(colIndex++).setCellValue(getStatusText(billing.getStatus()));
                row.createCell(colIndex++).setCellValue(billing.getBillingRate() != null ? billing.getBillingRate().getName() : "");
                row.createCell(colIndex++).setCellValue(billing.getTotalConsumption() != null ? billing.getTotalConsumption() : 0);
                row.createCell(colIndex++).setCellValue(billing.getFixedFeeAmount() != null ? billing.getFixedFeeAmount().doubleValue() : 0);
                row.createCell(colIndex++).setCellValue(billing.getConsumptionAmount() != null ? billing.getConsumptionAmount().doubleValue() : 0);
                row.createCell(colIndex++).setCellValue(billing.getDiscountAmount() != null ? billing.getDiscountAmount().doubleValue() : 0);
                row.createCell(colIndex++).setCellValue(billing.getTaxAmount() != null ? billing.getTaxAmount().doubleValue() : 0);
                row.createCell(colIndex++).setCellValue(billing.getTotalAmount() != null ? billing.getTotalAmount().doubleValue() : 0);
                
                if (includeCustomerDetails) {
                    row.createCell(colIndex++).setCellValue(billing.getCustomer().getDni() != null ? billing.getCustomer().getDni() : "");
                    row.createCell(colIndex++).setCellValue(billing.getCustomer().getMainEmailAddress() != null ? billing.getCustomer().getMainEmailAddress() : "");
                    row.createCell(colIndex++).setCellValue(billing.getCustomer().getMainPhoneNumber() != null ? billing.getCustomer().getMainPhoneNumber() : "");
                    row.createCell(colIndex++).setCellValue(billing.getCustomer().getFullAddress() != null ? billing.getCustomer().getFullAddress() : "");
                }
                
                if (includeComments) {
                    row.createCell(colIndex++).setCellValue(billing.getComments() != null ? billing.getComments() : "");
                }
            }
            
            // Auto-size columns
            for (int i = 0; i < colIndex; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            throw new RuntimeException("Error generating Excel export", e);
        }
    }

    /**
     * Export billings to CSV format.
     *
     * @param billings List of billings to export
     * @param includeCustomerDetails Include customer details
     * @param includeItemDetails Include item details
     * @param includeComments Include comments
     * @return CSV file as byte array
     */
    public byte[] exportToCSV(List<Billing> billings, boolean includeCustomerDetails, 
                             boolean includeItemDetails, boolean includeComments) {
        StringBuilder csv = new StringBuilder();
        
        // Create header row
        csv.append("Nº Factura,Cliente,Período,Fecha Emisión,Fecha Vencimiento,Estado,Tarifa,")
           .append("Consumo Total (kWh),Cuota Fija (€),Consumo (€),Descuento (€),IVA (€),Total (€)");
        
        if (includeCustomerDetails) {
            csv.append(",DNI Cliente,Email Cliente,Teléfono Cliente,Dirección Cliente");
        }
        
        if (includeComments) {
            csv.append(",Comentarios");
        }
        
        csv.append("\n");
        
        // Add data rows
        for (Billing billing : billings) {
            csv.append(billing.getBillNumber()).append(",")
               .append("\"").append(billing.getCustomer().getFirstName()).append(" ").append(billing.getCustomer().getLastName()).append("\"").append(",")
               .append(billing.getBillingPeriod().toString()).append(",")
               .append(billing.getIssueDate().toString()).append(",")
               .append(billing.getDueDate() != null ? billing.getDueDate().toString() : "").append(",")
               .append(getStatusText(billing.getStatus())).append(",")
               .append("\"").append(billing.getBillingRate() != null ? billing.getBillingRate().getName() : "").append("\"").append(",")
               .append(billing.getTotalConsumption() != null ? billing.getTotalConsumption() : 0).append(",")
               .append(billing.getFixedFeeAmount() != null ? billing.getFixedFeeAmount().doubleValue() : 0).append(",")
               .append(billing.getConsumptionAmount() != null ? billing.getConsumptionAmount().doubleValue() : 0).append(",")
               .append(billing.getDiscountAmount() != null ? billing.getDiscountAmount().doubleValue() : 0).append(",")
               .append(billing.getTaxAmount() != null ? billing.getTaxAmount().doubleValue() : 0).append(",")
               .append(billing.getTotalAmount() != null ? billing.getTotalAmount().doubleValue() : 0);
            
            if (includeCustomerDetails) {
                csv.append(",\"").append(billing.getCustomer().getDni() != null ? billing.getCustomer().getDni() : "").append("\"")
                   .append(",\"").append(billing.getCustomer().getMainEmailAddress() != null ? billing.getCustomer().getMainEmailAddress() : "").append("\"")
                   .append(",\"").append(billing.getCustomer().getMainPhoneNumber() != null ? billing.getCustomer().getMainPhoneNumber() : "").append("\"")
                   .append(",\"").append(billing.getCustomer().getFullAddress() != null ? billing.getCustomer().getFullAddress() : "").append("\"");
            }
            
            if (includeComments) {
                csv.append(",\"").append(billing.getComments() != null ? billing.getComments().replace("\"", "\"\"") : "").append("\"");
            }
            
            csv.append("\n");
        }
        
        return csv.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * Export multiple billings as PDFs in a ZIP file.
     *
     * @param billings List of billings to export
     * @return ZIP file as byte array
     */
    public byte[] exportBulkPDFs(List<Billing> billings) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {
            
            for (Billing billing : billings) {
                // Generate PDF for each billing
                ByteArrayOutputStream pdfOutput = billingPdfService.generateBillingPdf(billing);
                
                // Create ZIP entry
                String filename = String.format("Factura_%d_%s_%s.pdf", 
                    billing.getBillNumber(),
                    billing.getCustomer().getLastName().replaceAll("[^a-zA-Z0-9]", "_"),
                    billing.getBillingPeriod().toString());
                
                ZipEntry entry = new ZipEntry(filename);
                zos.putNextEntry(entry);
                zos.write(pdfOutput.toByteArray());
                zos.closeEntry();
            }
            
            zos.finish();
            return baos.toByteArray();
            
        } catch (IOException e) {
            throw new RuntimeException("Error generating bulk PDF export", e);
        }
    }

    private String getStatusText(BillingStatus status) {
        return switch (status) {
            case DRAFT -> "Borrador";
            case PENDING -> "Pendiente";
            case PAID -> "Pagada";
        };
    }
}

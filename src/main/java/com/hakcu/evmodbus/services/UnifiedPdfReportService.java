package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.utils.SvgToPdfImageConverter;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Unified service for generating PDF reports based on current filter selections.
 * Consolidates all PDF generation logic into a single, flexible system.
 */
@Service
public class UnifiedPdfReportService {

    // Constants for common strings
    private static final String CONSUMPTION_LABEL = "Consumo (kWh)";
    private static final String PERIOD_LABEL = "Período";
    private static final String HOUR_LABEL = "Hora";
    private static final String DAY_LABEL = "Día";
    private static final String MONTH_LABEL = "Mes";
    private static final String NO_DATA_MESSAGE = "No hay datos disponibles para este período.";
    private static final String NO_CHART_DATA_MESSAGE = "No hay datos disponibles para generar el gráfico.";
    private static final String CHART_ERROR_PREFIX = "Error al generar el gráfico: ";
    private static final String DATE_FORMAT_PATTERN = "dd/MM/yyyy";
    private static final String SHORT_DATE_FORMAT_PATTERN = "dd/MM";
    private static final String TIME_FORMAT_PATTERN = "HH:mm";
    private static final Locale SPANISH_LOCALE = Locale.of("es", "ES");

    // Constants for chart dimensions and styling (consistent with billing PDF)
    private static final float CHART_ASPECT_RATIO = 0.5f; // Reduced for better summary page layout
    private static final Font TITLE_FONT = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
    private static final Font SUBTITLE_FONT = new Font(Font.FontFamily.HELVETICA, 14, Font.BOLD);
    private static final Font SECTION_TITLE_FONT = new Font(Font.FontFamily.HELVETICA, 16, Font.BOLD);
    private static final Font NORMAL_FONT = new Font(Font.FontFamily.HELVETICA, 12, Font.NORMAL);
    private static final Font SMALL_FONT = new Font(Font.FontFamily.HELVETICA, 10, Font.NORMAL);
    private static final Font HEADER_FONT = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD);
    private static final BaseColor LIGHT_COLOR = new BaseColor(248, 249, 250);
    private static final BaseColor BORDER_COLOR = new BaseColor(222, 226, 230);
    private static final BaseColor PRIMARY_COLOR = new BaseColor(13, 110, 253); // Bootstrap primary blue

    // Page layout constants
    private static final int MAX_ROWS_PER_PAGE = 35; // Maximum data rows per page for detailed tables

    private final StatisticsService statisticsService;
    private final ChartService chartService;
    private final SvgToPdfImageConverter svgConverter;

    public UnifiedPdfReportService(StatisticsService statisticsService, ChartService chartService, SvgToPdfImageConverter svgConverter) {
        this.statisticsService = statisticsService;
        this.chartService = chartService;
        this.svgConverter = svgConverter;
    }

    /**
     * Generates a unified PDF report based on the current filter selections.
     * This method dynamically creates reports for the selected time period and date range.
     *
     * @param spotId        The ID of the spot.
     * @param reportType    The type of report (day, month, year).
     * @param selectedDay   The selected day (for daily reports).
     * @param selectedMonth The selected month (for monthly reports).
     * @param selectedYear  The selected year (for yearly reports).
     * @return ByteArrayOutputStream containing the PDF.
     * @throws RuntimeException If there's an error generating the report.
     */
    public ByteArrayOutputStream generateUnifiedReport(Long spotId, String reportType,
                                                      LocalDate selectedDay, String selectedMonth, Integer selectedYear) {
        try {
            // Initialize document with professional layout
            DocumentSetup docSetup = initializeDocument();
            Document document = docSetup.document();

            // Get spot information
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new IllegalArgumentException("Spot not found"));

            // Generate report based on type
            switch (reportType.toLowerCase()) {
                case "day" -> generateDailyReport(document, spot, selectedDay);
                case "month" -> generateMonthlyReport(document, spot, selectedMonth);
                case "year" -> generateYearlyReport(document, spot, selectedYear);
                default -> throw new IllegalArgumentException("Invalid report type: " + reportType);
            }

            document.close();
            return docSetup.outputStream();
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid parameters for unified report: " + e.getMessage(), e);
        } catch (DocumentException e) {
            throw new RuntimeException("Error creating PDF document for unified report", e);
        } catch (IOException e) {
            throw new RuntimeException("Error processing chart data for unified report", e);
        } catch (Exception e) {
            throw new RuntimeException("Unexpected error generating unified report", e);
        }
    }

    /**
     * Generates a daily report with multi-page layout.
     */
    private void generateDailyReport(Document document, Spot spot, LocalDate day) throws DocumentException, IOException {
        if (day == null) {
            day = LocalDate.now();
        }

        boolean isCurrentDay = day.equals(LocalDate.now());

        // PAGE 1: Summary Page
        generateSummaryPage(document, spot, day, isCurrentDay, "daily");

        // PAGE 2+: Detailed Data Pages
        generateDetailedDataPages(document, spot.getId(), day, isCurrentDay, "daily");
    }

    /**
     * Generates a monthly report with multi-page layout.
     */
    private void generateMonthlyReport(Document document, Spot spot, String selectedMonth) throws DocumentException, IOException {
        YearMonth yearMonth;
        if (selectedMonth == null || selectedMonth.isEmpty()) {
            yearMonth = YearMonth.now();
        } else {
            yearMonth = YearMonth.parse(selectedMonth);
        }

        boolean isCurrentMonth = yearMonth.equals(YearMonth.now());

        // PAGE 1: Summary Page
        generateSummaryPage(document, spot, yearMonth, isCurrentMonth, "monthly");

        // PAGE 2+: Detailed Data Pages
        generateDetailedDataPages(document, spot.getId(), yearMonth, isCurrentMonth, "monthly");
    }

    /**
     * Generates a yearly report with multi-page layout.
     */
    private void generateYearlyReport(Document document, Spot spot, Integer selectedYear) throws DocumentException, IOException {
        int year = (selectedYear != null) ? selectedYear : LocalDate.now().getYear();
        boolean isCurrentYear = year == LocalDate.now().getYear();

        // PAGE 1: Summary Page
        generateSummaryPage(document, spot, year, isCurrentYear, "yearly");

        // PAGE 2+: Detailed Data Pages
        generateDetailedDataPages(document, spot.getId(), year, isCurrentYear, "yearly");
    }

    /**
     * Initializes a new PDF document with professional layout.
     */
    private DocumentSetup initializeDocument() throws DocumentException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 36, 36, 54, 54); // Professional margins
        PdfWriter.getInstance(document, outputStream);
        document.open();
        return new DocumentSetup(document, outputStream);
    }

    /**
     * Adds a professional title to the document.
     */
    private void addTitle(Document document, String title) throws DocumentException {
        Paragraph titleParagraph = new Paragraph(title, TITLE_FONT);
        titleParagraph.setAlignment(Element.ALIGN_CENTER);
        titleParagraph.setSpacingAfter(10);
        document.add(titleParagraph);
    }

    /**
     * Adds a professional subtitle to the document.
     */
    private void addSubtitle(Document document, String subtitle) throws DocumentException {
        Paragraph subtitleParagraph = new Paragraph(subtitle, SUBTITLE_FONT);
        subtitleParagraph.setAlignment(Element.ALIGN_CENTER);
        subtitleParagraph.setSpacingAfter(20);
        document.add(subtitleParagraph);
    }

    /**
     * Adds a section title to the document.
     */
    private void addSectionTitle(Document document, String title) throws DocumentException {
        Paragraph sectionTitle = new Paragraph(title, SECTION_TITLE_FONT);
        sectionTitle.setSpacingBefore(15);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);
    }

    /**
     * Generates the summary page (Page 1) for all report types.
     */
    private void generateSummaryPage(Document document, Spot spot, Object period, boolean isCurrent, String reportType)
            throws DocumentException, IOException {

        // Add title and subtitle
        String title = getSummaryPageTitle(reportType);
        String subtitle = getSummaryPageSubtitle(spot, period, isCurrent, reportType);

        addTitle(document, title);
        addSubtitle(document, subtitle);

        // Add summary consumption table
        addSummaryConsumptionTable(document, spot.getId(), period, isCurrent, reportType);

        // Add chart visualization
        addSummaryChart(document, spot.getId(), period, isCurrent, reportType);
    }

    /**
     * Generates detailed data pages (Page 2+) for all report types.
     */
    private void generateDetailedDataPages(Document document, Long spotId, Object period, boolean isCurrent, String reportType)
            throws DocumentException {

        // Start new page for detailed data
        document.newPage();

        // Add detailed data section title
        String detailedTitle = getDetailedDataTitle(reportType);
        addSectionTitle(document, detailedTitle);

        // Get detailed data based on report type
        Map<String, Float> detailedData = getDetailedData(spotId, period, isCurrent, reportType);

        if (detailedData.isEmpty()) {
            addNoDataMessage(document);
            return;
        }

        // Add detailed data table with pagination
        addPaginatedDetailedDataTable(document, detailedData, reportType);
    }

    /**
     * Calculates a date range for a day.
     */
    private DateRange calculateDayDateRange(LocalDate day, boolean isCurrentDay) {
        LocalDateTime dayStart = day.atStartOfDay();
        LocalDateTime dayEnd = isCurrentDay ? LocalDateTime.now() : day.atTime(23, 59, 59);
        return new DateRange(dayStart, dayEnd);
    }

    /**
     * Calculates a date range for a month.
     */
    private DateRange calculateMonthDateRange(YearMonth yearMonth, boolean isCurrentMonth) {
        LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime monthEnd = isCurrentMonth ? LocalDateTime.now() : yearMonth.atEndOfMonth().atTime(23, 59, 59);
        return new DateRange(monthStart, monthEnd);
    }

    /**
     * Calculates a date range for a year.
     */
    private DateRange calculateYearDateRange(int year, boolean isCurrentYear) {
        LocalDateTime yearStart = LocalDateTime.of(year, 1, 1, 0, 0);
        LocalDateTime yearEnd = isCurrentYear ? LocalDateTime.now() : LocalDateTime.of(year, 12, 31, 23, 59, 59);
        return new DateRange(yearStart, yearEnd);
    }

    /**
     * Creates a professional consumption table with data.
     */
    private PdfPTable createConsumptionTable(Map<String, Float> data, String labelHeader) {
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);
        table.setSpacingAfter(15);

        // Add headers with professional styling
        table.addCell(createHeaderCell(labelHeader));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data rows with alternating colors
        boolean alternate = false;
        for (Map.Entry<String, Float> entry : data.entrySet()) {
            table.addCell(createDataCell(entry.getKey(), alternate));
            table.addCell(createDataCell(String.format("%.2f", entry.getValue()), alternate));
            alternate = !alternate;
        }

        return table;
    }

    /**
     * Creates a professional header cell for tables.
     */
    private PdfPCell createHeaderCell(String text) {
        PdfPCell cell = new PdfPCell(new Phrase(text, HEADER_FONT));
        cell.setBackgroundColor(PRIMARY_COLOR);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setPadding(8);
        cell.setBorderColor(PRIMARY_COLOR);
        return cell;
    }

    /**
     * Creates a professional data cell for tables.
     */
    private PdfPCell createDataCell(String text, boolean alternate) {
        PdfPCell cell = new PdfPCell(new Phrase(text, NORMAL_FONT));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setPadding(6);
        cell.setBackgroundColor(alternate ? LIGHT_COLOR : BaseColor.WHITE);
        cell.setBorderColor(BORDER_COLOR);
        return cell;
    }

    /**
     * Adds a professional chart to the PDF document.
     */
    private void addChartToPdf(Document document, Map<String, Float> data, String chartTitle,
                               String yAxisLabel, String description) throws DocumentException {
        if (data.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        try {
            // Set chart dimensions for optimal layout
            float pageWidth = document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin();
            float pageHeight = pageWidth * CHART_ASPECT_RATIO;

            // Generate chart SVG
            String barChartSvg = chartService.generateBarChartSvg(data, (int) pageWidth, (int) pageHeight, chartTitle, yAxisLabel);

            // Add description with professional styling
            Paragraph chartDescription = new Paragraph(description, NORMAL_FONT);
            chartDescription.setAlignment(Element.ALIGN_CENTER);
            chartDescription.setSpacingAfter(10);
            document.add(chartDescription);

            // Add chart to PDF
            svgConverter.addSvgToPdf(document, barChartSvg, pageWidth, pageHeight);
        } catch (IOException e) {
            document.add(new Paragraph(CHART_ERROR_PREFIX + e.getMessage(), SMALL_FONT));
        }
    }

    /**
     * Handles the case when no data is available for a section.
     */
    private void addNoDataMessage(Document document) throws DocumentException {
        Paragraph noDataParagraph = new Paragraph(NO_DATA_MESSAGE, NORMAL_FONT);
        noDataParagraph.setAlignment(Element.ALIGN_CENTER);
        noDataParagraph.setSpacingAfter(15);
        document.add(noDataParagraph);
    }

    /**
     * Gets the appropriate title for the summary page based on report type.
     */
    private String getSummaryPageTitle(String reportType) {
        return switch (reportType) {
            case "daily" -> "Informe de Consumo Diario";
            case "monthly" -> "Informe de Consumo Mensual";
            case "yearly" -> "Informe de Consumo Anual";
            default -> "Informe de Consumo";
        };
    }

    /**
     * Gets the appropriate subtitle for the summary page.
     */
    private String getSummaryPageSubtitle(Spot spot, Object period, boolean isCurrent, String reportType) {
        String baseSubtitle = "Plaza " + spot.getSpotNumber() + " - Sótano " + spot.getFloor().getFloorNumber();

        return switch (reportType) {
            case "daily" -> {
                LocalDate day = (LocalDate) period;
                String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
                String suffix = isCurrent ? " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern(TIME_FORMAT_PATTERN)) + ")" : "";
                yield formattedDay + " - " + baseSubtitle + suffix;
            }
            case "monthly" -> {
                YearMonth yearMonth = (YearMonth) period;
                String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
                String suffix = isCurrent ? " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern(SHORT_DATE_FORMAT_PATTERN)) + ")" : "";
                yield monthName + " " + yearMonth.getYear() + " - " + baseSubtitle + suffix;
            }
            case "yearly" -> {
                Integer year = (Integer) period;
                String suffix = isCurrent ? " (hasta " + YearMonth.now().getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE) + ")" : "";
                yield "Año " + year + " - " + baseSubtitle + suffix;
            }
            default -> baseSubtitle;
        };
    }

    /**
     * Gets the appropriate title for detailed data pages.
     */
    private String getDetailedDataTitle(String reportType) {
        return switch (reportType) {
            case "daily" -> "Datos Detallados de Consumo por Hora";
            case "monthly" -> "Datos Detallados de Consumo Diario";
            case "yearly" -> "Datos Detallados de Consumo Mensual";
            default -> "Datos Detallados de Consumo";
        };
    }

    /**
     * Adds summary consumption table for the summary page.
     */
    private void addSummaryConsumptionTable(Document document, Long spotId, Object period, boolean isCurrent, String reportType)
            throws DocumentException {

        addSectionTitle(document, "Resumen de Consumo");

        // Create professional summary table
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(70); // Smaller width for summary page
        table.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.setSpacingAfter(20);

        // Add headers
        table.addCell(createHeaderCell(PERIOD_LABEL));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data based on report type
        switch (reportType) {
            case "daily" -> addDailySummaryData(table, spotId, (LocalDate) period, isCurrent);
            case "monthly" -> addMonthlySummaryData(table, spotId, (YearMonth) period, isCurrent);
            case "yearly" -> addYearlySummaryData(table, spotId, (Integer) period, isCurrent);
        }

        document.add(table);
    }

    /**
     * Adds summary chart for the summary page.
     */
    private void addSummaryChart(Document document, Long spotId, Object period, boolean isCurrent, String reportType)
            throws DocumentException, IOException {

        addSectionTitle(document, "Gráfico de Consumo");

        // Get chart data based on report type
        Map<String, Float> chartData = getChartData(spotId, period, isCurrent, reportType);

        if (chartData.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        // Generate chart title and description
        String chartTitle = getChartTitle(period, isCurrent, reportType);
        String chartDescription = getChartDescription(period, reportType);

        // Add chart to PDF with optimized size for summary page
        addChartToPdf(document, chartData, chartTitle, CONSUMPTION_LABEL, chartDescription);
    }

    /**
     * Gets detailed data based on report type.
     */
    private Map<String, Float> getDetailedData(Long spotId, Object period, boolean isCurrent, String reportType) {
        return switch (reportType) {
            case "daily" -> {
                LocalDate day = (LocalDate) period;
                DateRange dayRange = calculateDayDateRange(day, isCurrent);
                yield statisticsService.getHourlyConsumption(spotId, dayRange.start(), dayRange.end());
            }
            case "monthly" -> {
                YearMonth yearMonth = (YearMonth) period;
                DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrent);
                yield statisticsService.getDailyConsumption(spotId, monthRange.start(), monthRange.end());
            }
            case "yearly" -> {
                Integer year = (Integer) period;
                DateRange yearRange = calculateYearDateRange(year, isCurrent);
                yield statisticsService.getMonthlyConsumption(spotId, yearRange.start(), yearRange.end());
            }
            default -> new LinkedHashMap<>();
        };
    }

    /**
     * Gets chart data based on report type.
     */
    private Map<String, Float> getChartData(Long spotId, Object period, boolean isCurrent, String reportType) {
        // For summary page, we use the same data as detailed data
        return getDetailedData(spotId, period, isCurrent, reportType);
    }

    /**
     * Adds paginated detailed data table that spans multiple pages if needed.
     */
    private void addPaginatedDetailedDataTable(Document document, Map<String, Float> data, String reportType)
            throws DocumentException {

        String labelHeader = switch (reportType) {
            case "daily" -> HOUR_LABEL;
            case "monthly" -> DAY_LABEL;
            case "yearly" -> MONTH_LABEL;
            default -> PERIOD_LABEL;
        };

        // Convert data to list for pagination
        java.util.List<Map.Entry<String, Float>> dataList = new java.util.ArrayList<>(data.entrySet());

        int totalRows = dataList.size();
        int currentRow = 0;
        int pageNumber = 1;

        while (currentRow < totalRows) {
            // Add page number for subsequent pages
            if (pageNumber > 1) {
                document.newPage();
                addSectionTitle(document, getDetailedDataTitle(reportType) + " (Página " + pageNumber + ")");
            }

            // Create table for this page
            PdfPTable table = new PdfPTable(2);
            table.setWidthPercentage(100);
            table.setSpacingAfter(15);

            // Add headers
            table.addCell(createHeaderCell(labelHeader));
            table.addCell(createHeaderCell(CONSUMPTION_LABEL));

            // Add data rows for this page
            int rowsOnThisPage = 0;
            boolean alternate = false;

            while (currentRow < totalRows && rowsOnThisPage < MAX_ROWS_PER_PAGE) {
                Map.Entry<String, Float> entry = dataList.get(currentRow);
                table.addCell(createDataCell(entry.getKey(), alternate));
                table.addCell(createDataCell(String.format("%.2f", entry.getValue()), alternate));

                alternate = !alternate;
                currentRow++;
                rowsOnThisPage++;
            }

            document.add(table);
            pageNumber++;
        }
    }

    /**
     * Gets chart title based on report type and period.
     */
    private String getChartTitle(Object period, boolean isCurrent, String reportType) {
        return switch (reportType) {
            case "daily" -> {
                LocalDate day = (LocalDate) period;
                String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
                String suffix = isCurrent ? " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern(TIME_FORMAT_PATTERN)) + ")" : "";
                yield "Consumo por hora - " + formattedDay + suffix;
            }
            case "monthly" -> {
                YearMonth yearMonth = (YearMonth) period;
                String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
                String suffix = isCurrent ? " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern(SHORT_DATE_FORMAT_PATTERN)) + ")" : "";
                yield "Consumo diario - " + monthName + " " + yearMonth.getYear() + suffix;
            }
            case "yearly" -> {
                Integer year = (Integer) period;
                String suffix = isCurrent ? " (hasta " + YearMonth.now().getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE) + ")" : "";
                yield "Consumo mensual - " + year + suffix;
            }
            default -> "Consumo";
        };
    }

    /**
     * Gets chart description based on report type and period.
     */
    private String getChartDescription(Object period, String reportType) {
        return switch (reportType) {
            case "daily" -> {
                LocalDate day = (LocalDate) period;
                String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
                yield "Consumo por hora para el día " + formattedDay;
            }
            case "monthly" -> {
                YearMonth yearMonth = (YearMonth) period;
                String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
                yield "Consumo diario para " + monthName + " " + yearMonth.getYear();
            }
            case "yearly" -> {
                Integer year = (Integer) period;
                yield "Consumo mensual para el año " + year;
            }
            default -> "Datos de consumo";
        };
    }

    /**
     * Adds daily summary data to the summary table.
     */
    private void addDailySummaryData(PdfPTable table, Long spotId, LocalDate day, boolean isCurrentDay) {
        // Calculate consumption for the day
        DateRange dayRange = calculateDayDateRange(day, isCurrentDay);
        Float dailyConsumption = statisticsService.getTotalConsumption(spotId, dayRange.start(), dayRange.end());

        // Calculate consumption for the month
        YearMonth yearMonth = YearMonth.from(day);
        boolean isCurrentMonth = isCurrentDay && day.getMonth() == LocalDate.now().getMonth() && day.getYear() == LocalDate.now().getYear();
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Float monthlyConsumption = statisticsService.getTotalConsumption(spotId, monthRange.start(), monthRange.end());

        // Add data rows
        String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
        table.addCell(createDataCell(formattedDay, false));
        table.addCell(createDataCell(String.format("%.2f", dailyConsumption), false));

        String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
        table.addCell(createDataCell(monthName + " " + yearMonth.getYear(), true));
        table.addCell(createDataCell(String.format("%.2f", monthlyConsumption), true));
    }

    /**
     * Adds monthly summary data to the summary table.
     */
    private void addMonthlySummaryData(PdfPTable table, Long spotId, YearMonth yearMonth, boolean isCurrentMonth) {
        // Calculate consumption for the month
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Float monthlyConsumption = statisticsService.getTotalConsumption(spotId, monthRange.start(), monthRange.end());

        // Calculate consumption for the year
        boolean isCurrentYear = isCurrentMonth && yearMonth.getYear() == LocalDate.now().getYear();
        DateRange yearRange = calculateYearDateRange(yearMonth.getYear(), isCurrentYear);
        Float yearlyConsumption = statisticsService.getTotalConsumption(spotId, yearRange.start(), yearRange.end());

        // Add data rows
        String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
        table.addCell(createDataCell(monthName + " " + yearMonth.getYear(), false));
        table.addCell(createDataCell(String.format("%.2f", monthlyConsumption), false));

        table.addCell(createDataCell("Año " + yearMonth.getYear() + " (hasta la fecha)", true));
        table.addCell(createDataCell(String.format("%.2f", yearlyConsumption), true));
    }

    /**
     * Adds yearly summary data to the summary table.
     */
    private void addYearlySummaryData(PdfPTable table, Long spotId, Integer year, boolean isCurrentYear) {
        // Calculate consumption for the year
        DateRange yearRange = calculateYearDateRange(year, isCurrentYear);
        Float yearlyConsumption = statisticsService.getTotalConsumption(spotId, yearRange.start(), yearRange.end());

        // Add data row
        table.addCell(createDataCell("Año " + year, false));
        table.addCell(createDataCell(String.format("%.2f", yearlyConsumption), false));
    }



    /**
     * Record to hold document and output stream together.
     */
    private record DocumentSetup(Document document, ByteArrayOutputStream outputStream) {
    }

    /**
     * Record to represent a date range with start and end times.
     */
    private record DateRange(LocalDateTime start, LocalDateTime end) {
    }
}

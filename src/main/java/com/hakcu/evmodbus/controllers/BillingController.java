package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.entities.Billing;
import com.hakcu.evmodbus.entities.BillingItem;
import com.hakcu.evmodbus.entities.BillingRate;
import com.hakcu.evmodbus.entities.Customer;
import com.hakcu.evmodbus.enums.BillingStatus;
import com.hakcu.evmodbus.services.BillingRateService;
import com.hakcu.evmodbus.services.BillingService;
import com.hakcu.evmodbus.services.CustomerService;
import com.hakcu.evmodbus.utils.PaginationUtils;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Controller for billing-related operations.
 */
@Controller
@RequestMapping("/billing")
@Secured({"ROLE_ADMIN", "ROLE_MANAGER"})
public class BillingController {

    private final BillingService billingService;
    private final CustomerService customerService;
    private final BillingRateService billingRateService;

    public BillingController(BillingService billingService, CustomerService customerService, BillingRateService billingRateService) {
        this.billingService = billingService;
        this.customerService = customerService;
        this.billingRateService = billingRateService;
    }

    /**
     * Lists all billings with optional filtering.
     */
    @GetMapping
    public String listAll(
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") YearMonth period,
            @RequestParam(required = false) Boolean paid,
            @RequestParam(required = false) Long billNumber,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {

        // Add next available bill number for the modal
        model.addAttribute("nextBillNumber", billingService.getNextBillNumber());

        // Add customers for filter dropdown
        List<Customer> customers = customerService.findAll();
        model.addAttribute("customers", customers);

        // Add billing rates for the modal
        List<BillingRate> billingRates = billingRateService.findAll();
        model.addAttribute("billingRates", billingRates);

        // Check if any billing rates exist and add to model
        boolean billingRatesExist = billingRateService.existsAnyBillingRate();
        model.addAttribute("billingRatesExist", billingRatesExist);

        // Add the last valid billing period (previous month) to the model for the modal
        YearMonth lastValidBillingPeriod = YearMonth.now().minusMonths(1);
        model.addAttribute("lastValidBillingPeriod", lastValidBillingPeriod);

        // Add empty billing rate for the modal form
        BillingRate newBillingRate = new BillingRate();
        newBillingRate.setFixedMonthlyFee(BigDecimal.valueOf(0));
        model.addAttribute("billingRate", newBillingRate);

        // Add filter parameters to model
        model.addAttribute("customerId", customerId);
        model.addAttribute("period", period);
        model.addAttribute("paid", paid);
        model.addAttribute("billNumber", billNumber);

        // Get paginated billings with filters
        Page<Billing> billingPage = billingService.findPaginatedWithFilters(
                customerId, period, paid, billNumber, page, size);

        // Add pagination attributes
        PaginationUtils.addPaginationAttributes(model, page, size, billingPage);
        model.addAttribute("billings", billingPage.getContent());

        return "billing/list";
    }

    /**
     * Shows details for a specific billing.
     */
    @GetMapping("/{id}")
    public String details(@PathVariable Long id, Model model) {
        Billing billing = billingService.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Factura no encontrada"));
        model.addAttribute("billing", billing);
        return "billing/details";
    }

    /**
     * Shows details for a specific billing by bill number.
     */
    @GetMapping("/number/{billNumber}")
    public String detailsByBillNumber(@PathVariable Long billNumber, Model model, RedirectAttributes redirectAttributes) {
        try {
            Billing billing = billingService.findByBillNumber(billNumber)
                    .orElseThrow(() -> new EntityNotFoundException("Factura no encontrada"));
            model.addAttribute("billing", billing);
            return "billing/details";
        } catch (EntityNotFoundException e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Factura no encontrada");
            return "redirect:/billing";
        }
    }

    /**
     * Generates billings based on the customer selection.
     * If customerId is "all", generates billings for all customers.
     * Otherwise, generates a billing for the specified customer.
     */
    @PostMapping("/generate-unified")
    public String generateUnifiedBilling(
            @RequestParam String customerId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM") YearMonth period,
            @RequestParam Long billingRateId,
            @RequestParam(required = false) Long billNumber,
            RedirectAttributes redirectAttributes,
            HttpServletRequest request,
            HttpServletResponse response) {

        // Check if any billing rates exist
        if (!billingRateService.existsAnyBillingRate()) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "No se pueden generar facturas porque no existen tarifas de facturación. Por favor, cree al menos una tarifa.");
            addHtmxRedirectHeaderIfNeeded(response, "/billing");
            return "redirect:/billing";
        }

        // Additional validation for period
        if (period == null) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Debe seleccionar un período válido (mes y año) para generar la factura");
            addHtmxRedirectHeaderIfNeeded(response, "/billing");
            return "redirect:/billing";
        }

        // Validate that the period is not the current month or a future month
        YearMonth currentMonth = YearMonth.now();
        if (period.compareTo(currentMonth) >= 0) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Solo se pueden generar facturas para meses completos (anteriores al mes actual). " +
                    "No se pueden generar facturas para el mes actual o meses futuros.");
            addHtmxRedirectHeaderIfNeeded(response, "/billing");
            return "redirect:/billing";
        }

        // Validate bill number if provided
        if (billNumber != null && !billingService.isValidBillNumber(billNumber)) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "El número de factura proporcionado no es válido. Debe ser mayor que " +
                    (billingService.getNextBillNumber() - 1));
            addHtmxRedirectHeaderIfNeeded(response, "/billing");
            return "redirect:/billing";
        }

        // Check if generating for all customers or a specific customer
        if ("all".equals(customerId)) {
            try {
                // For batch generation, we now allow custom bill numbers as starting point
                List<Billing> billings = billingService.generateBillingForAllCustomers(period, billingRateId, billNumber);
                redirectAttributes.addFlashAttribute("successMessage",
                        "Se han generado " + billings.size() + " facturas para el período " + period);
                String redirectUrl = "/billing?period=" + period;
                addHtmxRedirectHeaderIfNeeded(response, redirectUrl);
                return "redirect:" + redirectUrl;
            } catch (Exception e) {
                redirectAttributes.addFlashAttribute("errorMessage",
                        "Error al generar las facturas: " + e.getMessage());
                addHtmxRedirectHeaderIfNeeded(response, "/billing");
                return "redirect:/billing";
            }
        } else {
            try {
                Long customerIdLong = Long.parseLong(customerId);
                Billing billing = billingService.generateBilling(customerIdLong, period, billingRateId);

                // Set custom bill number if provided
                if (billNumber != null) {
                    billing.setBillNumber(billNumber);
                    billing = billingService.save(billing);
                }
                redirectAttributes.addFlashAttribute("successMessage",
                        "Factura generada correctamente para " + billing.getCustomer().getFirstName() +
                        " " + billing.getCustomer().getLastName() + " - " + period);
                String redirectUrl = "/billing/" + billing.getId();
                addHtmxRedirectHeaderIfNeeded(response, redirectUrl);
                return "redirect:" + redirectUrl;
            } catch (NumberFormatException e) {
                redirectAttributes.addFlashAttribute("errorMessage",
                        "ID de cliente inválido");
                addHtmxRedirectHeaderIfNeeded(response, "/billing");
                return "redirect:/billing";
            } catch (Exception e) {
                redirectAttributes.addFlashAttribute("errorMessage",
                        "Error al generar la factura: " + e.getMessage());
                addHtmxRedirectHeaderIfNeeded(response, "/billing");
                return "redirect:/billing";
            }
        }
    }



    /**
     * Marks a billing as paid.
     */
    @PostMapping("/{id}/mark-paid")
    public String markAsPaid(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            billingService.markAsPaid(id);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Factura marcada como pagada correctamente");
            return "redirect:/billing/" + id;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al marcar la factura como pagada: " + e.getMessage());
            return "redirect:/billing/" + id;
        }
    }

    /**
     * Marks a billing as unpaid.
     */
    @PostMapping("/{id}/mark-unpaid")
    public String markAsUnpaid(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            billingService.markAsUnpaid(id);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Factura marcada como no pagada correctamente");
            return "redirect:/billing/" + id;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al marcar la factura como no pagada: " + e.getMessage());
            return "redirect:/billing/" + id;
        }
    }

    /**
     * Finalizes a draft billing by changing its status to PENDING.
     */
    @PostMapping("/{id}/finalize")
    public String finalizeDraft(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            billingService.finalizeDraft(id);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Factura finalizada correctamente");
            return "redirect:/billing/" + id;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al finalizar la factura: " + e.getMessage());
            return "redirect:/billing/" + id;
        }
    }

    /**
     * Shows the form to edit a billing item in DRAFT status.
     */
    @GetMapping("/{billingId}/items/{itemId}/edit")
    public String editBillingItem(
            @PathVariable Long billingId,
            @PathVariable Long itemId,
            Model model,
            RedirectAttributes redirectAttributes) {
        try {
            Billing billing = billingService.findById(billingId)
                    .orElseThrow(() -> new EntityNotFoundException("Factura no encontrada"));

            // Only allow editing items if billing is in DRAFT status
            if (billing.getStatus() != BillingStatus.DRAFT) {
                redirectAttributes.addFlashAttribute("errorMessage",
                        "Solo se pueden editar facturas en estado borrador");
                return "redirect:/billing/" + billingId;
            }

            // Find the billing item
            BillingItem item = billing.getItems().stream()
                    .filter(i -> i.getId().equals(itemId))
                    .findFirst()
                    .orElseThrow(() -> new EntityNotFoundException("Ítem de factura no encontrado"));

            model.addAttribute("billing", billing);
            model.addAttribute("item", item);

            return "billing/item-edit";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al cargar el ítem para edición: " + e.getMessage());
            return "redirect:/billing/" + billingId;
        }
    }

    /**
     * Updates a billing item in DRAFT status.
     */
    @PostMapping("/{billingId}/items/{itemId}")
    public String updateBillingItem(
            @PathVariable Long billingId,
            @PathVariable Long itemId,
            @RequestParam Float consumption,
            @RequestParam BigDecimal rateApplied,
            RedirectAttributes redirectAttributes) {
        try {
            billingService.updateBillingItem(billingId, itemId, consumption, rateApplied);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Ítem de factura actualizado correctamente");
            return "redirect:/billing/" + billingId;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al actualizar el ítem de factura: " + e.getMessage());
            return "redirect:/billing/" + billingId;
        }
    }

    /**
     * Shows the form to edit a draft billing.
     */
    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes) {
        try {
            Billing billing = billingService.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Factura no encontrada"));

            // Only allow editing if billing is in DRAFT status
            if (billing.getStatus() != BillingStatus.DRAFT) {
                redirectAttributes.addFlashAttribute("errorMessage",
                        "Solo se pueden editar facturas en estado borrador");
                return "redirect:/billing/" + id;
            }

            // Add billing rates for dropdown
            List<BillingRate> billingRates = billingRateService.findAll();

            model.addAttribute("billing", billing);
            model.addAttribute("billingRates", billingRates);

            return "billing/edit";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al cargar el formulario de edición: " + e.getMessage());
            return "redirect:/billing/" + id;
        }
    }

    /**
     * Updates a draft billing.
     */
    @PostMapping("/{id}/edit")
    public String updateDraftBilling(
            @PathVariable Long id,
            @RequestParam Long billNumber,
            @RequestParam Long billingRateId,
            @RequestParam(required = false) BigDecimal discountAmount,
            @RequestParam(required = false) String comments,
            RedirectAttributes redirectAttributes,
            HttpServletResponse response) {
        try {
            // Validate discount amount
            if (discountAmount == null) {
                discountAmount = BigDecimal.ZERO;
            } else if (discountAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("El descuento no puede ser negativo");
            }

            billingService.updateDraftBilling(id, billNumber, billingRateId, discountAmount, comments);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Factura actualizada correctamente");
            String redirectUrl = "/billing/" + id;
            addHtmxRedirectHeaderIfNeeded(response, redirectUrl);
            return "redirect:" + redirectUrl;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al actualizar la factura: " + e.getMessage());
            return "redirect:/billing/" + id + "/edit";
        }
    }

    /**
     * Deletes a billing.
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    public void delete(@PathVariable Long id) {
        billingService.delete(id);
    }

    /**
     * HTMX endpoint to toggle the warning visibility based on customer selection.
     */
    @GetMapping("/toggle-warning")
    public String toggleWarning(@RequestParam(required = false) String customerId, Model model) {
        boolean showWarning = "all".equals(customerId);
        model.addAttribute("showWarning", showWarning);
        return "billing/fragments/warning :: warning";
    }

    /**
     * Bulk operation to mark multiple billings as paid.
     */
    @PostMapping("/bulk/mark-paid")
    public ResponseEntity<?> bulkMarkPaid(@RequestParam List<Long> billingIds, RedirectAttributes redirectAttributes) {
        try {
            int updatedCount = billingService.bulkUpdateStatus(billingIds, BillingStatus.PAID);
            return ResponseEntity.ok().body(Map.of(
                "success", true,
                "message", "Se han marcado " + updatedCount + " facturas como pagadas",
                "updatedCount", updatedCount
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Error al actualizar las facturas: " + e.getMessage()
            ));
        }
    }

    /**
     * Bulk operation to mark multiple billings as pending.
     */
    @PostMapping("/bulk/mark-pending")
    public ResponseEntity<?> bulkMarkPending(@RequestParam List<Long> billingIds, RedirectAttributes redirectAttributes) {
        try {
            int updatedCount = billingService.bulkUpdateStatus(billingIds, BillingStatus.PENDING);
            return ResponseEntity.ok().body(Map.of(
                "success", true,
                "message", "Se han marcado " + updatedCount + " facturas como pendientes",
                "updatedCount", updatedCount
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Error al actualizar las facturas: " + e.getMessage()
            ));
        }
    }

    /**
     * Export billings to Excel format.
     */
    @GetMapping("/export/excel")
    public ResponseEntity<byte[]> exportToExcel(
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") YearMonth period,
            @RequestParam(required = false) Boolean paid,
            @RequestParam(required = false) Long billNumber,
            @RequestParam(defaultValue = "true") boolean includeCustomerDetails,
            @RequestParam(defaultValue = "false") boolean includeItemDetails,
            @RequestParam(defaultValue = "false") boolean includeComments) {
        try {
            // Get filtered billings
            List<Billing> billings = billingService.findAllWithFilters(customerId, period, paid, billNumber);

            // Generate Excel file
            byte[] excelData = billingService.exportToExcel(billings, includeCustomerDetails, includeItemDetails, includeComments);

            String filename = "facturas_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".xlsx";

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(excelData);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Export billings to CSV format.
     */
    @GetMapping("/export/csv")
    public ResponseEntity<byte[]> exportToCSV(
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") YearMonth period,
            @RequestParam(required = false) Boolean paid,
            @RequestParam(required = false) Long billNumber,
            @RequestParam(defaultValue = "true") boolean includeCustomerDetails,
            @RequestParam(defaultValue = "false") boolean includeItemDetails,
            @RequestParam(defaultValue = "false") boolean includeComments) {
        try {
            // Get filtered billings
            List<Billing> billings = billingService.findAllWithFilters(customerId, period, paid, billNumber);

            // Generate CSV file
            byte[] csvData = billingService.exportToCSV(billings, includeCustomerDetails, includeItemDetails, includeComments);

            String filename = "facturas_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv";

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.parseMediaType("text/csv"))
                    .body(csvData);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Bulk export PDFs as ZIP file.
     */
    @PostMapping("/bulk/export-pdf")
    public ResponseEntity<byte[]> bulkExportPDF(@RequestParam List<Long> billingIds) {
        try {
            // Get billings by IDs (only non-draft ones)
            List<Billing> billings = billingService.findByIdsAndNotDraft(billingIds);

            if (billings.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            // Generate ZIP file with PDFs
            byte[] zipData = billingService.exportBulkPDFs(billings);

            String filename = "facturas_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".zip";

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.parseMediaType("application/zip"))
                    .body(zipData);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Helper method to add HX-Redirect header for HTMX requests.
     * This ensures proper redirection after form submission with HTMX.
     * Also adds a script to clean up any Bootstrap modal artifacts.
     */
    private void addHtmxRedirectHeaderIfNeeded(HttpServletResponse response, String redirectUrl) {
        // Add the HX-Redirect header for HTMX to handle the redirect
        response.setHeader("HX-Redirect", redirectUrl);

        // Add a script to clean up Bootstrap modal artifacts before redirecting
        // This helps prevent scrolling issues on the target page
        String cleanupScript =
                "<script>" +
                        "document.body.classList.remove('modal-open');" +
                        "document.body.style.removeProperty('overflow');" +
                        "document.body.style.removeProperty('padding-right');" +
                        "const modalBackdrops = document.querySelectorAll('.modal-backdrop');" +
                        "modalBackdrops.forEach(backdrop => backdrop.remove());" +
                        "</script>";
        response.setHeader("HX-Trigger", "{\"modalCleanup\": " + cleanupScript + "}");
    }
}

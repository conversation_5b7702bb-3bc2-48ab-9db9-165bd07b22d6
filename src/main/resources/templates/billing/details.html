<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Detalle de factura</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Detalle de factura')}"></th:block>
<div class="container-sm mb-5">
    <!-- Alert messages -->
    <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${successMessage}">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage}">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Draft status notice -->
    <div class="alert alert-info mb-4" th:if="${billing.status.name() == 'DRAFT'}">
        <i class="bi bi-info-circle-fill me-2"></i>
        <strong>Esta factura está en estado borrador.</strong> Puede editar los ítems de consumo y luego finalizar el borrador para convertirla en una factura pendiente.
        Una vez finalizada, no se podrán realizar más cambios en los ítems.
    </div>

    <!-- Enhanced billing header card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="card-title mb-0 me-3">
                    <i class="bi bi-receipt me-2"></i>
                    Factura #<span th:text="${billing.billNumber}"></span>
                </h5>
                <!-- Status badge -->
                <span class="badge rounded-pill text-bg-success fs-6" th:if="${billing.status.name() == 'PAID'}">
                    <i class="bi bi-check-circle me-1"></i>Pagada
                </span>
                <span class="badge rounded-pill text-bg-warning fs-6" th:if="${billing.status.name() == 'PENDING'}">
                    <i class="bi bi-clock me-1"></i>Pendiente
                </span>
                <span class="badge rounded-pill text-bg-secondary fs-6" th:if="${billing.status.name() == 'DRAFT'}">
                    <i class="bi bi-pencil me-1"></i>Borrador
                </span>
            </div>
            <div class="btn-group">
                <a th:href="@{/billing}" class="btn btn-outline-secondary" data-bs-toggle="tooltip" title="Volver a la lista">
                    <i class="bi bi-arrow-left me-2"></i>Volver
                </a>
                <a th:if="${billing.status.name() == 'DRAFT'}" th:href="@{/billing/{id}/edit(id=${billing.id})}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="Editar borrador">
                    <i class="bi bi-pencil-square me-2"></i>Editar
                </a>
                <a th:if="${billing.status.name() != 'DRAFT'}" th:href="@{/billing/export/pdf/{id}(id=${billing.id})}" hx-boost="false" class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Descargar PDF">
                    <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                </a>
                <div class="dropdown">
                    <button class="btn btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="Más acciones">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="printBilling()">
                            <i class="bi bi-printer me-2"></i>Imprimir
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="emailBilling()">
                            <i class="bi bi-envelope me-2"></i>Enviar por email
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="duplicateBilling()">
                            <i class="bi bi-files me-2"></i>Duplicar factura
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Billing information -->
                <div class="col-md-6">
                    <h6 class="fw-bold">Información de facturación</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Número de factura:</th>
                            <td th:text="${billing.billNumber}"></td>
                        </tr>
                        <tr>
                            <th>Período:</th>
                            <td th:text="${#temporals.format(billing.billingPeriod, 'MMMM yyyy', new java.util.Locale('es', 'ES'))}"></td>
                        </tr>
                        <tr>
                            <th>Fecha de emisión:</th>
                            <td th:text="${#temporals.format(billing.issueDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Fecha de vencimiento:</th>
                            <td th:text="${#temporals.format(billing.dueDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Estado:</th>
                            <td>
                                <span class="badge rounded-pill text-bg-success" th:if="${billing.status.name() == 'PAID'}">Pagada</span>
                                <span class="badge rounded-pill text-bg-warning" th:if="${billing.status.name() == 'PENDING'}">Pendiente</span>
                                <span class="badge rounded-pill text-bg-secondary" th:if="${billing.status.name() == 'DRAFT'}">Borrador</span>
                            </td>
                        </tr>
                        <tr th:if="${billing.paid && billing.paidDate != null}">
                            <th>Fecha de pago:</th>
                            <td th:text="${#temporals.format(billing.paidDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Tarifa aplicada:</th>
                            <td>
                                <span th:if="${billing.billingRate != null}" th:text="${billing.billingRate.name}"></span>
                                <span th:if="${billing.billingRate == null}" class="text-muted">Sin tarifa</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Customer information -->
                <div class="col-md-6">
                    <h6 class="fw-bold">Información del cliente</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Nombre:</th>
                            <td th:text="${billing.customer.firstName + ' ' + billing.customer.lastName}"></td>
                        </tr>
                        <tr th:if="${billing.customer.dni != null && !billing.customer.dni.isEmpty()}">
                            <th>DNI:</th>
                            <td th:text="${billing.customer.dni}"></td>
                        </tr>
                        <tr th:if="${billing.customer.address != null && !billing.customer.address.isEmpty()}">
                            <th>Dirección:</th>
                            <td th:text="${billing.customer.fullAddress}"></td>
                        </tr>
                        <tr th:if="${billing.customer.mainEmailAddress != null && !billing.customer.mainEmailAddress.isEmpty()}">
                            <th>Email:</th>
                            <td th:text="${billing.customer.mainEmailAddress}"></td>
                        </tr>
                        <tr th:if="${billing.customer.mainPhoneNumber != null && !billing.customer.mainPhoneNumber.isEmpty()}">
                            <th>Teléfono:</th>
                            <td th:text="${billing.customer.mainPhoneNumber}"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="card-footer d-flex justify-content-between align-items-center">
            <div>
                <span class="fw-bold">Consumo total:</span>
                <span th:text="${#numbers.formatDecimal(billing.totalConsumption, 1, 2) + ' kWh'}"></span>
            </div>
            <div>
                <!-- Draft status actions -->
                <form th:if="${billing.status.name() == 'DRAFT'}" th:action="@{/billing/{id}/finalize(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Finalizar borrador
                    </button>
                </form>

                <!-- Pending status actions -->
                <form th:if="${billing.status.name() == 'PENDING'}" th:action="@{/billing/{id}/mark-paid(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>Marcar como pagada
                    </button>
                </form>

                <!-- Paid status actions -->
                <form th:if="${billing.status.name() == 'PAID'}" th:action="@{/billing/{id}/mark-unpaid(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-x-circle me-2"></i>Marcar como no pagada
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Billing items card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Detalle de consumo
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Plaza</th>
                            <th>Consumo (kWh)</th>
                            <th>Tarifa (€/kWh)</th>
                            <th>Importe (€)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item : ${billing.items}">
                            <td th:text="'Plaza ' + ${item.spot.spotNumber} + ' - Sótano ' + ${item.spot.floor.floorNumber}"></td>
                            <td th:text="${#numbers.formatDecimal(item.consumption, 1, 2)}"></td>
                            <td th:text="${#numbers.formatDecimal(item.rateApplied, 1, 2) + ' €'}"></td>
                            <td th:text="${#numbers.formatDecimal(item.amount, 1, 2) + ' €'}"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Comments card - only shown if comments exist -->
    <div class="card shadow-sm mb-4" th:if="${billing.comments != null && !billing.comments.trim().isEmpty()}">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-chat-left-text me-2"></i>
                Comentarios
            </h5>
        </div>
        <div class="card-body">
            <p class="mb-0" th:text="${billing.comments}"></p>
        </div>
    </div>

    <!-- Billing summary card -->
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-calculator me-2"></i>
                Resumen de facturación
            </h5>
        </div>
        <div class="card-body">
            <div class="row justify-content-end">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <th>Cuota fija:</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.fixedFeeAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>Consumo:</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.consumptionAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>Subtotal:</th>
                            <td class="text-end" th:with="subtotal=${billing.fixedFeeAmount.add(billing.consumptionAmount)}"
                                th:text="${#numbers.formatDecimal(subtotal, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr th:if="${billing.discountAmount != null && billing.discountAmount.compareTo(T(java.math.BigDecimal).ZERO) > 0}">
                            <th>Descuento:</th>
                            <td class="text-end text-danger" th:text="${'-' + #numbers.formatDecimal(billing.discountAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr th:if="${billing.discountAmount != null && billing.discountAmount.compareTo(T(java.math.BigDecimal).ZERO) > 0}">
                            <th>Subtotal con descuento:</th>
                            <td class="text-end" th:with="subtotalWithDiscount=${billing.fixedFeeAmount.add(billing.consumptionAmount).subtract(billing.discountAmount)}"
                                th:text="${#numbers.formatDecimal(subtotalWithDiscount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>IVA (21%):</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.taxAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr class="table-active">
                            <th class="fs-5">TOTAL:</th>
                            <td class="text-end fs-5 fw-bold" th:text="${#numbers.formatDecimal(billing.totalAmount, 1, 2) + ' €'}"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Enhanced JavaScript functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Remove any Bootstrap modal classes that might be left on the body
        document.body.classList.remove('modal-open');

        // Remove any inline styles that might be affecting scrolling
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('padding-right');

        // Force the body to be scrollable
        document.body.style.overflow = 'auto';

        // Remove any modal backdrops that might still be present
        const modalBackdrops = document.querySelectorAll('.modal-backdrop');
        modalBackdrops.forEach(backdrop => {
            backdrop.remove();
        });
    });

    // Enhanced action functions
    function printBilling() {
        // Open PDF in new window for printing
        const pdfUrl = '/billing/export/pdf/' + getBillingId();
        const printWindow = window.open(pdfUrl, '_blank');
        printWindow.onload = function() {
            printWindow.print();
        };
    }

    function emailBilling() {
        // Show email modal or redirect to email functionality
        alert('Funcionalidad de envío por email próximamente disponible');
        // In a real implementation, this would open an email modal or send directly
    }

    function duplicateBilling() {
        if (confirm('¿Está seguro de que desea duplicar esta factura? Se creará una nueva factura en estado borrador.')) {
            // In a real implementation, this would call a backend endpoint
            alert('Funcionalidad de duplicación próximamente disponible');
        }
    }

    function getBillingId() {
        // Extract billing ID from URL or data attribute
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1];
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+P for print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printBilling();
        }

        // Ctrl+E for edit (if in draft status)
        if (e.ctrlKey && e.key === 'e') {
            const editBtn = document.querySelector('a[href*="/edit"]');
            if (editBtn) {
                e.preventDefault();
                editBtn.click();
            }
        }

        // Escape to go back
        if (e.key === 'Escape') {
            window.location.href = '/billing';
        }
    });
</script>
</body>
</html>
